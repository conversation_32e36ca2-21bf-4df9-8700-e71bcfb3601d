from flask import render_template, redirect, url_for, flash, request, g, abort
from flask_login import login_required, current_user
from app import db
from app.admin import bp
from app.models import User, <PERSON><PERSON><PERSON>, GiftList, Gift
from functools import wraps
from app.translations import get_translation
from sqlalchemy.orm import joinedload

# Helper function for translation
def _(text):
    """Translate text using the current language"""
    if hasattr(g, 'lang_code'):
        return get_translation(text, g.lang_code)
    return text

# Admin required decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash(_('You do not have permission to access this page.'), 'danger')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def index():
    """Admin dashboard"""
    return render_template('admin/index.html')

@bp.route('/users')
@login_required
@admin_required
def users():
    """User management page"""
    # Get all users with their OAuth information
    users = User.query.all()

    # Get OAuth information for each user
    oauth_info = {}
    oauth_entries = OAuth.query.all()

    for oauth in oauth_entries:
        oauth_info[oauth.user_id] = {
            'provider': oauth.provider,
            'created_at': oauth.created_at
        }



    return render_template('admin/users.html', users=users, oauth_info=oauth_info)

@bp.route('/users/<int:user_id>')
@login_required
@admin_required
def user_detail(user_id):
    """User detail page"""
    user = User.query.get_or_404(user_id)

    # Get OAuth information
    oauth_info = OAuth.query.filter_by(user_id=user.id).first()
    is_oauth_user = oauth_info is not None



    # Get user's gift lists
    gift_lists = user.gift_lists.all()

    return render_template('admin/user_detail.html',
                          user=user,
                          is_oauth_user=is_oauth_user,
                          oauth_info=oauth_info,
                          gift_lists=gift_lists)

@bp.route('/users/<int:user_id>/toggle-role', methods=['POST'])
@login_required
@admin_required
def toggle_role(user_id):
    """Toggle user role between user and admin"""
    user = User.query.get_or_404(user_id)

    # Don't allow changing your own role
    if user.id == current_user.id:
        flash(_('You cannot change your own role.'), 'danger')
        return redirect(url_for('admin.users'))

    # Toggle role
    if user.role == 'admin':
        user.role = 'user'
        flash(_('User role changed to regular user.'), 'success')
    else:
        user.role = 'admin'
        flash(_('User role changed to admin.'), 'success')

    db.session.commit()

    # Check if we're coming from the user detail page
    referrer = request.referrer
    if referrer and f'/admin/users/{user_id}' in referrer:
        return redirect(url_for('admin.user_detail', user_id=user_id))

    return redirect(url_for('admin.users'))

@bp.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """Delete a user and all their data"""
    user = User.query.get_or_404(user_id)

    # Don't allow admins to delete their own account
    if user.id == current_user.id:
        flash(_('You cannot delete your own account.'), 'danger')
        return redirect(url_for('admin.users'))

    try:
        # Get username for the success message
        username = user.username

        # Delete all OAuth entries
        OAuth.query.filter_by(user_id=user.id).delete()

        # Delete all gifts reserved by this user
        Gift.query.filter_by(reserved_by_id=user.id).update({'reserved': False, 'reserved_by_id': None})

        # Delete all gift lists and their gifts (cascade delete will handle this)
        # The relationship is set up with cascade='all, delete-orphan' in the User model

        # Delete the user
        db.session.delete(user)
        db.session.commit()

        flash(_('User {} has been deleted successfully.').format(username), 'success')
    except Exception as e:
        db.session.rollback()
        flash(_('An error occurred while deleting the user: {}').format(str(e)), 'danger')

    return redirect(url_for('admin.users'))
