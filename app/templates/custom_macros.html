{% macro render_flash_messages() %}
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3 mb-5" style="z-index: 1100;">
        {% for category, message in messages %}
          <div class="toast align-items-center text-white bg-{{ category }} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
              <div class="toast-body py-2">
                <i class="bi {% if category == 'success' %}bi-check-circle{% elif category == 'danger' %}bi-exclamation-triangle{% elif category == 'warning' %}bi-exclamation-circle{% else %}bi-info-circle{% endif %} me-1"></i>
                {{ message | safe }}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}
{% endmacro %}

{% macro render_ajax_alert_container() %}
  <div id="ajax-toast-container" class="toast-container position-fixed bottom-0 end-0 p-3 mb-5" style="z-index: 1100;">
    <!-- Ajax alerts will be inserted here dynamically -->
  </div>
{% endmacro %}
