{% from 'custom_macros.html' import render_flash_messages %}
<!DOCTYPE html>
<html lang="{{ g.lang_code }}">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>{% block title %}FiestaMagic{% endblock %}</title>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">
  <!-- Tabler CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/css/tabler.min.css" />
  <!-- Tabler JS -->
  <script src="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/js/tabler.min.js"></script>
  {% block styles %}{% endblock %}
</head>
<body class="layout-navbar">
  <div class="page">
    <!-- Tabler Navbar Example -->
    <header class="navbar navbar-expand-md d-print-none">
      <div class="container-xl">
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
          <span class="navbar-toggler-icon"></span>
        </button>
        <a class="navbar-brand" href="{{ url_for('main.index') }}">
          <img src="{{ url_for('static', filename='images/logo.svg') }}" width="32" height="32" alt="Logo">
          FiestaMagic
        </a>
        <div class="collapse navbar-collapse" id="navbar-menu">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('main.index') }}">{{ _('Home') }}</a>
            </li>
            {% if current_user.is_authenticated %}
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <img src="{{ current_user.gravatar(size=30) }}" alt="{{ current_user.username }}" class="rounded-circle me-1" width="30" height="30">
                {{ current_user.username }}
              </a>
              <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">{{ _('Profile') }}</a></li>
                {% if current_user.is_admin() %}
                <li><a class="dropdown-item" href="{{ url_for('admin.index') }}">{{ _('Admin Panel') }}</a></li>
                {% endif %}
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">{{ _('Logout') }}</a></li>
              </ul>
            </li>
            {% else %}
            <li class="nav-item"><a class="nav-link" href="{{ url_for('auth.login') }}">{{ _('Login') }}</a></li>
            <li class="nav-item"><a class="nav-link" href="{{ url_for('auth.register') }}">{{ _('Register') }}</a></li>
            {% endif %}
          </ul>
        </div>
      </div>
    </header>
    <div class="page-wrapper">
      <div class="container-xl mt-3">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">{{ _('Home') }}</a></li>
            {% block breadcrumbs %}{% endblock %}
          </ol>
        </nav>
        {{ render_flash_messages() }}
        <div class="content-container py-4 mb-5">
          {% block content %}{% endblock %}
        </div>
      </div>
    </div>
    <footer class="footer footer-transparent d-print-none">
      <div class="container-xl text-center">
        <p class="mb-0">© 2025 FiestaMagic | {{ _('All rights reserved') }}</p>
        <div class="mt-3">
          <div class="mb-2 small">
            {{ _('Current language') }}:
            <a href="{{ url_for('set_language', lang='en', next=request.path) }}" class="{% if g.get('lang_code') == 'en' %}fw-bold text-primary{% else %}text-muted{% endif %}">English</a> |
            <a href="{{ url_for('set_language', lang='pl', next=request.path) }}" class="{% if g.get('lang_code') == 'pl' %}fw-bold text-primary{% else %}text-muted{% endif %}">Polski</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
  {% block scripts %}{% endblock %}
</body>
</html>
