{% extends 'base.html' %}

{% block title %}{{ _('Add Gift') }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('main.gift_list_view', list_id=gift_list.unique_id) }}">{{ gift_list.name }}</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ _('Add Gift') }}</li>
{% endblock %}

{% block content %}
<div class="container-xl py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm rounded-3">
                <div class="card-header bg-light rounded-top-3">
                    <h2 class="mb-0 fs-4">{{ _('Add Gift to') }} "{{ gift_list.name }}"</h2>
                </div>
                <div class="card-body p-4">
                    <form id="addGiftForm" method="POST" action="{{ url_for('main.add_gift', list_id=gift_list.unique_id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
<!-- Gift Link Field -->
<div class="mb-4">
    <label for="link" class="form-label">{{ _('Gift Link') }}</label>
    <div class="input-group mb-2">
        <span class="input-group-text"><i class="bi bi-link"></i></span>
        <input type="url" class="form-control" id="link" name="link"
            placeholder="{{ _('https://example.com/product') }}" required>
    </div>
    <div class="form-text">{{ _('Enter the URL of the gift you want to add') }}</div>
</div>

<!-- Gift Name Field -->
<div class="mb-4">
    <label for="name" class="form-label">{{ _('Gift Name') }}</label>
    <div class="input-group mb-2">
        <span class="input-group-text"><i class="bi bi-tag"></i></span>
        <input type="text" class="form-control" id="name" name="name"
            placeholder="{{ _('Leave blank to use title from link') }}">
    </div>
    <div class="form-text" id="nameHelpText">{{ _('If left blank, we\'ll try to extract the title from the link') }}
    </div>
</div>

<!-- Buttons -->
<div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
    <a href="{{ url_for('main.gift_list_view', list_id=gift_list.unique_id) }}" class="btn btn-3 btn-outline-secondary">
        <i class="bi bi-x me-1"></i>{{ _('Back to List') }}
    </a>
    <button type="submit" class="btn btn-3 btn-primary" id="submitButton">
        <i class="bi bi-plus-circle me-1"></i>{{ _('Add Gift') }}
    </button>
</div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const linkInput = document.getElementById('link');
        const nameInput = document.getElementById('name');
        const nameHelpText = document.getElementById('nameHelpText');
        const submitButton = document.getElementById('submitButton');
        let fetchTimeout;

        // Function to fetch title from URL
        function fetchTitleFromUrl(url) {
            // Clear any existing timeout
            if (fetchTimeout) {
                clearTimeout(fetchTimeout);
            }

            // Show loading state
            nameHelpText.innerHTML = '<span class="text-info"><i class="bi bi-hourglass-split me-1"></i>{{ _("Fetching title from URL...") }}</span>';
            nameInput.placeholder = '{{ _("Fetching title from URL...") }}';

            // Set timeout for the fetch operation
            fetchTimeout = setTimeout(() => {
                nameHelpText.innerHTML = '<span class="text-warning">{{ _("Title fetch timed out. You can enter a name manually.") }}</span>';
                nameInput.placeholder = '{{ _("Leave blank to use title from link") }}';
            }, 10000); // 10 second timeout

            // Fetch title from server
            fetch(`{{ url_for('main.get_title') }}?url=${encodeURIComponent(url)}`)
                .then(response => response.json())
                .then(data => {
                    clearTimeout(fetchTimeout);
                    if (data.title && data.title !== 'Unknown Gift' && !data.title.startsWith('Gift from')) {
                        // Only auto-fill if the name field is empty
                        if (!nameInput.value.trim()) {
                            nameInput.value = data.title;
                        }
                        nameHelpText.innerHTML = '{{ _("If left blank, we\'ll try to extract the title from the link") }}';
                        nameInput.placeholder = '{{ _("Leave blank to use title from link") }}';
                    } else {
                        nameHelpText.innerHTML = '<span class="text-warning">{{ _("Could not fetch title. Please enter a name manually.") }}</span>';
                        nameInput.placeholder = '{{ _("Leave blank to use title from link") }}';
                    }
                })
                .catch(error => {
                    clearTimeout(fetchTimeout);
                    console.error('Error fetching title:', error);
                    nameHelpText.innerHTML = '<span class="text-warning">{{ _("Could not fetch title. Please enter a name manually.") }}</span>';
                    nameInput.placeholder = '{{ _("Leave blank to use title from link") }}';
                });
        }

        // Auto-fetch title when URL is entered
        linkInput.addEventListener('input', function () {
            const url = this.value.trim();
            if (url && url.startsWith('http')) {
                // Debounce the fetch request
                if (fetchTimeout) {
                    clearTimeout(fetchTimeout);
                }
                fetchTimeout = setTimeout(() => {
                    fetchTitleFromUrl(url);
                }, 1000); // Wait 1 second after user stops typing
            } else {
                // Reset help text if URL is invalid
                nameHelpText.innerHTML = '{{ _("If left blank, we\'ll try to extract the title from the link") }}';
                nameInput.placeholder = '{{ _("Leave blank to use title from link") }}';
            }
        });

        // Handle form submission
        document.getElementById('addGiftForm').addEventListener('submit', function (e) {
            const link = linkInput.value.trim();

            if (!link) {
                e.preventDefault();
                linkInput.focus();
                return false;
            }

            // Show loading state on submit button
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>{{ _("Adding...") }}';
        });
    });
</script>
{% endblock %}
