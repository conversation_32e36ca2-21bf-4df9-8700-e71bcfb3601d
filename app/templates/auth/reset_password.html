{% extends "base.html" %}

{% block title %}{{ _('Reset Password') }} - FiestaMagic{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{{ _('Reset Password') }}</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0"><i class="bi bi-key me-2"></i>{{ _('Reset Your Password') }}</h5>
            </div>
            <div class="card-body p-4">
                <p class="text-muted mb-4">{{ _('Please enter your new password.') }}</p>
                
                <form method="POST" action="{{ url_for('auth.reset_password', token=token) }}">
                    {{ form.csrf_token }}
                    <div class="mb-3">
                        <label for="password" class="form-label">{{ _('New Password') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock"></i></span>
                            {{ form.password(class="form-control", id="password") }}
                        </div>
                        {% for error in form.password.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{{ _('Confirm New Password') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                            {{ form.confirm_password(class="form-control", id="confirm_password") }}
                        </div>
                        {% for error in form.confirm_password.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-3 btn-primary">
                            <i class="bi bi-key me-1"></i> {{ _('Reset Password') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
