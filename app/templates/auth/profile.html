{% extends "base.html" %}

{% block title %}{{ _('Profile') }} - FiestaMagic{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{{ _('Profile') }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-transparent">
                <h5 class="mb-0"><i class="bi bi-person-circle me-2"></i>{{ _('User Information') }}</h5>
            </div>
            <div class="card-body p-4 text-center">
                <div class="mb-3">
                    <img src="{{ current_user.get_gravatar(size=150) }}" alt="{{ current_user.username }}"
                        class="rounded-circle img-thumbnail shadow-sm" width="150" height="150">
                </div>
                <h4>{{ current_user.username }}</h4>
                <p class="text-muted">{{ current_user.email }}</p>
                <p class="small text-muted">
                    {{ _('Member since') }}: {{ format_date(current_user.created_at) }}
                </p>
                <p class="small text-muted">
                    {{ _('Last seen') }}: {{ current_user.last_seen.strftime('%Y-%m-%d %H:%M') }}
                </p>
                <div class="mt-3 small text-muted">
                    <h6>{{ _('Profile Picture') }}</h6>
                    <p class="mb-1">{{ _('Your profile picture is managed through Gravatar') }}</p>
                    <a href="https://gravatar.com" target="_blank" class="btn btn-3 btn-outline-primary">
                        <i class="bi bi-image me-1"></i> {{ _('Change your picture at gravatar.com') }}
                    </a>
                </div>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>{{ _('Your Gift Lists') }}</h5>
            </div>
            <div class="card-body p-4">
                <p class="text-muted">{{ _('You have created') }} <strong>{{ current_user.gift_lists.count() }}</strong>
                    {{ _('gift lists') }}.</p>
                <a href="{{ url_for('main.index') }}" class="btn btn-3 btn-outline-primary">
                    <i class="bi bi-list me-1"></i> {{ _('View All Lists') }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-header bg-transparent">
                <h5 class="mb-0"><i class="bi bi-gear me-2"></i>{{ _('Edit Profile') }}</h5>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('auth.profile') }}">
                    {{ form.csrf_token }}
                    <div class="mb-3">
                        <label for="username" class="form-label">{{ _('Username') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-person"></i></span>
                            {{ form.username(class="form-control", id="username") }}
                        </div>
                        {% for error in form.username.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="theme" class="form-label">{{ _('Preferred Theme') }}</label>
                        {{ form.theme(class="form-select", id="theme") }}
                        {% for error in form.theme.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="language" class="form-label">{{ _('Preferred Language') }}</label>
                        {{ form.language(class="form-select", id="language") }}
                        {% for error in form.language.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-3 btn-primary">
                            <i class="bi bi-save me-1"></i> {{ _('Save Changes') }}
                        </button>
                    </div>
                </form>

                <hr class="my-4">

                <h6 class="mb-3">{{ _('Change Password') }}</h6>

                <form method="POST" action="{{ url_for('auth.change_password') }}">
                    {{ password_form.csrf_token }}
                    <div class="mb-3">
                        <label for="current_password" class="form-label">{{ _('Current Password') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock"></i></span>
                            {{ password_form.current_password(class="form-control", id="current_password") }}
                        </div>
                        {% for error in password_form.current_password.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="new_password" class="form-label">{{ _('New Password') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                            {{ password_form.new_password(class="form-control", id="new_password") }}
                        </div>
                        {% for error in password_form.new_password.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{{ _('Confirm New Password') }}</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                            {{ password_form.confirm_password(class="form-control", id="confirm_password") }}
                        </div>
                        {% for error in password_form.confirm_password.errors %}
                        <div class="text-danger">{{ _(error) }}</div>
                        {% endfor %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-3 btn-primary">
                            <i class="bi bi-key me-1"></i> {{ _('Change Password') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Get the theme select element
        const themeSelect = document.getElementById('theme');

        // Store the initial theme value
        const initialTheme = themeSelect.value;

        // Add change event listener to preview theme changes
        themeSelect.addEventListener('change', function () {
            // Apply the selected theme immediately for preview
            const selectedTheme = themeSelect.value;
            previewTheme(selectedTheme);
        });

        // Function to preview theme using Tabler's CSS class implementation
        function previewTheme(theme) {
            // Remove existing theme classes
            document.body.classList.remove('theme-light', 'theme-dark');

            if (theme === 'os') {
                // Use OS preference for preview
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.body.classList.add('theme-dark');
                } else {
                    document.body.classList.add('theme-light');
                }
            } else {
                // Set specific theme using CSS classes
                if (theme === 'dark') {
                    document.body.classList.add('theme-dark');
                } else {
                    document.body.classList.add('theme-light');
                }
            }
        }

        // When the form is submitted, update localStorage with the new theme
        const profileForm = document.querySelector('form[action="{{ url_for("auth.profile") }}"]');
        profileForm.addEventListener('submit', function () {
            localStorage.setItem('theme-preference', themeSelect.value);
        });
    });
</script>
{% endblock %}