<!DOCTYPE html>
<html lang="{{ g.get('lang_code', 'en') }}" data-bs-theme="{% if g.theme == 'dark' %}dark{% else %}light{% endif %}">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>{% block title %}FiestaMagic{% endblock %}</title>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">
  <!-- Tabler CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" />
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
<!-- Dark Mode Fixes -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/dark-mode-fixes.css') }}" />
  <style>
    .navbar-sticky[data-bs-sticky] {
      position: sticky !important;
      top: 0;
      z-index: 1100;
      background: #fff;
    }
    .sticky-navbar-container, .page, body, html {
      overflow: visible !important;
    }
  </style>
  <!-- Tabler JS -->
  <script src="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/js/tabler.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/js/tabler.sticky.min.js"></script>
  {% block styles %}{% endblock %}
</head>
<body class="layout-navbar layout-navbar-sticky {% if g.theme == 'dark' %}theme-dark{% else %}theme-light{% endif %}">
  <div class="sticky-navbar-container">
    {% from 'custom_macros.html' import render_flash_messages %}
    <div class="page">
      <!-- Tabler Sticky Navbar Example -->
      <header class="navbar navbar-expand-md navbar-sticky d-print-none" data-bs-sticky="true" data-bs-sticky-container=".sticky-navbar-container" style="z-index: 1100;">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
            <span class="navbar-toggler-icon"></span>
          </button>
          <a class="navbar-brand" href="{{ url_for('main.index') }}">
            <img src="{{ url_for('static', filename='images/logo.svg') }}" width="32" height="32" alt="Logo">
            FiestaMagic
          </a>
          <div class="collapse navbar-collapse" id="navbar-menu">
            <ul class="navbar-nav ms-auto">
              <li class="nav-item">
                <a class="nav-link" href="{{ url_for('main.index') }}">{{ _('Home') }}</a>
              </li>
            <!-- Theme Switcher -->
            <li class="nav-item">
                <a class="nav-link" href="#" id="theme-toggle" aria-label="{{ _('Toggle theme') }}">
                    <i id="theme-icon" class="bi bi-circle-half"></i>
                </a>
            </li>
              {% if current_user.is_authenticated %}
              <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                  <img src="{{ current_user.gravatar(size=30) }}" alt="{{ current_user.username }}" class="rounded-circle me-1" width="30" height="30">
                  {{ current_user.username }}
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                  <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">{{ _('Profile') }}</a></li>
                  {% if current_user.is_admin() %}
                  <li><a class="dropdown-item" href="{{ url_for('admin.index') }}">{{ _('Admin Panel') }}</a></li>
                  {% endif %}
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">{{ _('Logout') }}</a></li>
                </ul>
              </li>
              {% else %}
              <li class="nav-item"><a class="nav-link" href="{{ url_for('auth.login') }}">{{ _('Login') }}</a></li>
              <li class="nav-item"><a class="nav-link" href="{{ url_for('auth.register') }}">{{ _('Register') }}</a></li>
              {% endif %}
            </ul>
          </div>
        </div>
      </header>
      <div class="container-xl mt-5">
        <div class="card mb-4" style="background: #f8fafc; border-radius: .5rem; min-width: 0;">
          <div class="card-body py-2 px-3">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb mb-0 ps-0" style="background: transparent;">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">{{ _('Home') }}</a></li>
                {% block breadcrumbs %}{% endblock %}
              </ol>
            </nav>
          </div>
        </div>
      </div>
      <div class="container-xl">
        <div class="card shadow-sm mb-4">
          <div class="card-body p-3">
            {{ render_flash_messages() }}
            <div class="content-container">
              {% block content %}{% endblock %}
            </div>
          </div>
        </div>
      </div>
      <footer class="footer footer-transparent d-print-none">
        <div class="container-xl text-center">
          <p class="mb-0">© 2025 FiestaMagic | {{ _('All rights reserved') }}</p>
          <div class="mt-3">
            <div class="mb-2 small">
              {{ _('Current language') }}:
              <a href="{{ url_for('set_language', lang='en', next=request.path) }}" class="{% if g.get('lang_code') == 'en' %}fw-bold text-primary{% else %}text-muted{% endif %}">English</a> |
              <a href="{{ url_for('set_language', lang='pl', next=request.path) }}" class="{% if g.get('lang_code') == 'pl' %}fw-bold text-primary{% else %}text-muted{% endif %}">Polski</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
<!-- Theme Switcher Script -->
<script src="{{ url_for('static', filename='theme-switcher.js') }}"></script>
  {% block scripts %}{% endblock %}
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      if (window.Tabler && Tabler.Sticky) {
        console.log('Tabler.Sticky.init() called');
        Tabler.Sticky.init();
        // Log all sticky navbars found
        document.querySelectorAll('[data-bs-sticky]').forEach(function(el) {
          console.log('Sticky element:', el, 'offsetTop:', el.offsetTop, 'computed:', getComputedStyle(el).position);
        });
      } else {
        console.warn('Tabler.Sticky not found!');
      }
    });
  </script>
</body>
</html>