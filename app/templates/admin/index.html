{% extends "base.html" %}
{% block title %}{{ _('Admin Dashboard') }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active">{{ _('Admin Dashboard') }}</li>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="fw-bold mb-4">{{ _('Admin Dashboard') }}</h1>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm hover-lift">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-people-fill text-primary me-2"></i>
                                {{ _('User Management') }}
                            </h5>
                            <p class="card-text text-muted">
                                {{ _('Manage users and their roles') }}
                            </p>
                            <a href="{{ url_for('admin.users') }}" class="btn btn-3 btn-primary">
                                <i class="bi bi-arrow-right me-1"></i> {{ _('Manage Users') }}
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Additional admin modules can be added here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}
