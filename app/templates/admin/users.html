{% extends "base.html" %}
{% block title %}{{ _('User Management') }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">{{ _('Admin Dashboard') }}</a></li>
<li class="breadcrumb-item active">{{ _('User Management') }}</li>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="fw-bold mb-4">{{ _('User Management') }}</h1>

            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ _('ID') }}</th>
                                    <th>{{ _('Username') }}</th>
                                    <th>{{ _('Email') }}</th>
                                    <th>{{ _('Role') }}</th>
                                    <th>{{ _('Auth Method') }}</th>
                                    <th>{{ _('Created') }}</th>
                                    <th>{{ _('Last Seen') }}</th>
                                    <th>{{ _('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ user.gravatar(size=32) }}" alt="{{ user.username }}"
                                                class="rounded-circle me-2">
                                            <a href="{{ url_for('admin.user_detail', user_id=user.id) }}"
                                                class="text-decoration-none">
                                                {{ user.username }}
                                            </a>
                                        </div>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span
                                            class="badge {% if user.role == 'admin' %}bg-danger{% else %}bg-secondary{% endif %}">
                                            {{ user.role }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.id in oauth_info %}
                                        <span class="badge bg-primary"
                                            title="{{ _('Registered via') }} {{ oauth_info[user.id]['provider'] }}">
                                            <i class="bi bi-google me-1"></i> {{ _('Google') }}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-person-fill me-1"></i> {{ _('Email/Password') }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ format_date(user.created_at) }}</td>
                                    <td>{{ user.last_seen.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if user.id != current_user.id %}
                                        <div class="btn-group">
                                            <form action="{{ url_for('admin.toggle_role', user_id=user.id) }}"
                                                method="post" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit"
                                                    class="btn btn-3 {% if user.role == 'admin' %}btn-outline-secondary{% else %}btn-outline-danger{% endif %}">
                                                    {% if user.role == 'admin' %}
                                                    <i class="bi bi-person me-1"></i> {{ _('Make User') }}
                                                    {% else %}
                                                    <i class="bi bi-shield me-1"></i> {{ _('Make Admin') }}
                                                    {% endif %}
                                                </button>
                                            </form>
                                            <button type="button" class="btn btn-3 btn-outline-danger ms-1"
                                                data-bs-toggle="modal" data-bs-target="#deleteUserModal"
                                                data-id="{{ user.id }}" data-name="{{ user.username }}">
                                                <i class="bi bi-trash me-1"></i> {{ _('Delete') }}
                                            </button>
                                        </div>
                                        {% else %}
                                        <span class="text-muted">{{ _('Current User') }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Confirmation Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteUserModalLabel">{{ _('Confirm User Deletion') }}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ _('Are you sure you want to delete this user?') }}</p>
                <p class="fw-bold" id="deleteUserName"></p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {{ _('This action cannot be undone. All data associated with this user will be permanently deleted,
                    including:') }}
                    <ul class="mt-2 mb-0">
                        <li>{{ _('All gift lists created by this user') }}</li>
                        <li>{{ _('All gifts in those lists') }}</li>
                        <li>{{ _('All reservations made by this user') }}</li>
                        <li>{{ _('Authentication information') }}</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ _('Cancel') }}</button>
                <form id="deleteUserForm" action="" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i> {{ _('Delete User') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Handle delete user modal
        var deleteUserModal = document.getElementById('deleteUserModal');
        if (deleteUserModal) {
            deleteUserModal.addEventListener('show.bs.modal', function (event) {
                var button = event.relatedTarget;
                var userId = button.getAttribute('data-id');
                var userName = button.getAttribute('data-name');

                // Update the modal content
                document.getElementById('deleteUserName').textContent = userName;

                // Update the form action
                var form = document.getElementById('deleteUserForm');
                form.action = "{{ url_for('admin.delete_user', user_id=0) }}".replace('0', userId);
            });
        }
    });
</script>
{% endblock %}