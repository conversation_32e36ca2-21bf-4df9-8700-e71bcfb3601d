{% extends "base.html" %}
{% block title %}{{ _('User Details') }} - {{ user.username }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">{{ _('Admin Dashboard') }}</a></li>
<li class="breadcrumb-item"><a href="{{ url_for('admin.users') }}">{{ _('User Management') }}</a></li>
<li class="breadcrumb-item active">{{ _('User Details') }} - {{ user.username }}</li>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="fw-bold mb-4">{{ _('User Details') }}</h1>

            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-body text-center">
                            <img src="{{ user.gravatar(size=150) }}" alt="{{ user.username }}"
                                class="rounded-circle mb-3" width="150" height="150">
                            <h3 class="card-title">{{ user.username }}</h3>
                            <p class="text-muted">{{ user.email }}</p>

                            <div class="d-flex justify-content-center mb-3">
                                <span
                                    class="badge {% if user.role == 'admin' %}bg-danger{% else %}bg-secondary{% endif %} fs-6 px-3 py-2">
                                    {{ user.role }}
                                </span>
                            </div>

                            {% if user.id != current_user.id %}
                            <form action="{{ url_for('admin.toggle_role', user_id=user.id) }}" method="post"
                                class="mb-2">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit"
                                    class="btn btn-3 {% if user.role == 'admin' %}btn-outline-secondary{% else %}btn-outline-danger{% endif %} w-100">
                                    {% if user.role == 'admin' %}
                                    <i class="bi bi-person me-1"></i> {{ _('Make User') }}
                                    {% else %}
                                    <i class="bi bi-shield me-1"></i> {{ _('Make Admin') }}
                                    {% endif %}
                                </button>
                            </form>

                            <button type="button" class="btn btn-3 btn-outline-danger w-100" data-bs-toggle="modal"
                                data-bs-target="#deleteUserModal" data-id="{{ user.id }}"
                                data-name="{{ user.username }}">
                                <i class="bi bi-trash me-1"></i> {{ _('Delete User') }}
                            </button>
                            {% else %}
                            <div class="alert alert-info">
                                {{ _('This is your account') }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{{ _('Account Information') }}</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-hover">
                                <tbody>
                                    <tr>
                                        <th width="30%">{{ _('User ID') }}</th>
                                        <td>{{ user.id }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ _('Authentication Method') }}</th>
                                        <td>
                                            {% if is_oauth_user %}
                                            <span class="badge bg-primary">
                                                <i class="bi bi-google me-1"></i> {{ _('Google') }}
                                            </span>
                                            <small class="text-muted ms-2">{{ _('Connected on') }} {{
                                                format_date(oauth_info.created_at) }}</small>
                                            {% else %}
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-person-fill me-1"></i> {{ _('Email/Password') }}
                                            </span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{{ _('Account Created') }}</th>
                                        <td>{{ format_date(user.created_at) }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ _('Last Seen') }}</th>
                                        <td>{{ user.last_seen.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ _('Preferred Theme') }}</th>
                                        <td>
                                            {% if user.preferred_theme == 'light' %}
                                            <i class="bi bi-sun-fill text-warning me-1"></i> {{ _('Light') }}
                                            {% elif user.preferred_theme == 'dark' %}
                                            <i class="bi bi-moon-fill text-primary me-1"></i> {{ _('Dark') }}
                                            {% else %}
                                            <i class="bi bi-circle-half text-secondary me-1"></i> {{ _('OS Default') }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{{ _('Preferred Language') }}</th>
                                        <td>
                                            {% if user.preferred_language == 'en' %}
                                            <span class="fi fi-gb me-1"></span> {{ _('English') }}
                                            {% elif user.preferred_language == 'pl' %}
                                            <span class="fi fi-pl me-1"></span> {{ _('Polish') }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">{{ _('Gift Lists') }}</h5>
                        </div>
                        <div class="card-body">
                            {% if gift_lists %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>{{ _('Name') }}</th>
                                            <th>{{ _('Privacy') }}</th>
                                            <th>{{ _('Created') }}</th>
                                            <th>{{ _('Gifts') }}</th>
                                            <th>{{ _('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for list in gift_lists %}
                                        <tr>
                                            <td>{{ list.name }}</td>
                                            <td>
                                                {% if list.is_private %}
                                                <span class="badge bg-secondary">
                                                    <i class="bi bi-lock-fill me-1"></i> {{ _('Private') }}
                                                </span>
                                                {% else %}
                                                <span class="badge bg-success">
                                                    <i class="bi bi-unlock-fill me-1"></i> {{ _('Public') }}
                                                </span>
                                                {% endif %}
                                            </td>
                                            <td>{{ list.created_at.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ list.gifts.count() }}</td>
                                            <td>
                                                <a href="{{ url_for('main.gift_list_view', list_id=list.unique_id) }}"
                                                    class="btn btn-3 btn-primary">
                                                    <i class="bi bi-eye me-1"></i> {{ _('View') }}
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                {{ _('This user has not created any gift lists yet.') }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Confirmation Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteUserModalLabel">{{ _('Confirm User Deletion') }}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ _('Are you sure you want to delete this user?') }}</p>
                <p class="fw-bold">{{ user.username }}</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {{ _('This action cannot be undone. All data associated with this user will be permanently deleted,
                    including:') }}
                    <ul class="mt-2 mb-0">
                        <li>{{ _('All gift lists created by this user') }}</li>
                        <li>{{ _('All gifts in those lists') }}</li>
                        <li>{{ _('All reservations made by this user') }}</li>
                        <li>{{ _('Authentication information') }}</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-3 btn-secondary" data-bs-dismiss="modal">{{ _('Cancel') }}</button>
                <form action="{{ url_for('admin.delete_user', user_id=user.id) }}" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-3 btn-danger">
                        <i class="bi bi-trash me-1"></i> {{ _('Delete User') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}