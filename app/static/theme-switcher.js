// Theme switcher functionality
document.addEventListener('DOMContentLoaded', function () {
    // Theme values
    const THEME_LIGHT = 'light';
    const THEME_DARK = 'dark';
    const THEME_OS = 'os';

    // Get the theme toggle button
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.getElementById('theme-icon');
    const themeText = document.getElementById('theme-text');

    // Function to set theme using both data-bs-theme and CSS classes for compatibility
    function setTheme(theme) {
        // Save the theme preference
        localStorage.setItem('theme-preference', theme);

        // Remove existing theme classes
        document.body.classList.remove('theme-light', 'theme-dark');

        if (theme === THEME_OS) {
            // Use OS preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                document.documentElement.setAttribute('data-bs-theme', 'dark');
                document.body.classList.add('theme-dark');
            } else {
                document.documentElement.setAttribute('data-bs-theme', 'light');
                document.body.classList.add('theme-light');
            }
            // Update toggle button
            if (themeIcon) themeIcon.className = 'bi bi-circle-half';
            if (themeText) themeText.textContent = themeText.getAttribute('data-text-os') || 'OS Default';
        } else {
            // Set specific theme using both approaches for maximum compatibility
            document.documentElement.setAttribute('data-bs-theme', theme);

            if (theme === THEME_DARK) {
                document.body.classList.add('theme-dark');
                if (themeIcon) themeIcon.className = 'bi bi-moon-fill';
                if (themeText) themeText.textContent = themeText.getAttribute('data-text-dark') || 'Dark';
            } else {
                document.body.classList.add('theme-light');
                if (themeIcon) themeIcon.className = 'bi bi-sun-fill';
                if (themeText) themeText.textContent = themeText.getAttribute('data-text-light') || 'Light';
            }
        }
    }

    // Initialize theme based on saved preference or OS setting
    function initializeTheme() {
        // Check if the document has a theme attribute set from the server
        const serverTheme = document.documentElement.getAttribute('data-bs-theme');
        const savedTheme = localStorage.getItem('theme-preference');

        // If there's a saved theme preference, use it
        if (savedTheme) {
            setTheme(savedTheme);
        }
        // If no saved theme but server set a theme, save it to localStorage
        else if (serverTheme && serverTheme !== 'light') {
            localStorage.setItem('theme-preference', serverTheme);
            setTheme(serverTheme);
        }
            // Default to light theme (since OS preference might not work well with Tabler)
        else {
            setTheme(THEME_LIGHT);
        }
    }

    // Toggle between themes (Light -> Dark -> OS -> Light)
    function toggleTheme() {
        const currentTheme = localStorage.getItem('theme-preference') || THEME_OS;

        if (currentTheme === THEME_LIGHT) {
            setTheme(THEME_DARK);
        } else if (currentTheme === THEME_DARK) {
            setTheme(THEME_OS);
        } else {
            setTheme(THEME_LIGHT);
        }

        // No need to reload - Tabler handles theme switching smoothly
    }

    // Add event listener to theme toggle button
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Listen for OS theme changes
    if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function () {
            const savedTheme = localStorage.getItem('theme-preference');
            if (savedTheme === THEME_OS) {
                setTheme(THEME_OS);
            }
        });
    }

    // Initialize theme on page load
    initializeTheme();
});
