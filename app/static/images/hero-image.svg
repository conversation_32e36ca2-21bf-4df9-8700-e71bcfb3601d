<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Gift List Hero</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFD1DC" offset="0%"></stop>
            <stop stop-color="#FFA07A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#98FB98" offset="0%"></stop>
            <stop stop-color="#3CB371" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#ADD8E6" offset="0%"></stop>
            <stop stop-color="#4682B4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Gift-List">
            <rect id="Background" fill="#F8F9FA" x="0" y="0" width="800" height="600"></rect>
            
            <!-- Large Gift Box -->
            <g id="Large-Gift" transform="translate(400, 300)">
                <rect id="Box" fill="url(#linearGradient-1)" x="-100" y="-80" width="200" height="160" rx="10"></rect>
                <rect id="Ribbon-Vertical" fill="#FF6B6B" x="-10" y="-100" width="20" height="200"></rect>
                <rect id="Ribbon-Horizontal" fill="#FF6B6B" x="-120" y="-10" width="240" height="20"></rect>
                <circle id="Bow-Center" fill="#FF4757" cx="0" cy="0" r="15"></circle>
                <ellipse id="Bow-Left" fill="#FF4757" cx="-20" cy="-10" rx="15" ry="10" transform="rotate(-30)"></ellipse>
                <ellipse id="Bow-Right" fill="#FF4757" cx="20" cy="-10" rx="15" ry="10" transform="rotate(30)"></ellipse>
            </g>
            
            <!-- Medium Gift Box -->
            <g id="Medium-Gift" transform="translate(200, 350)">
                <rect id="Box" fill="url(#linearGradient-2)" x="-60" y="-50" width="120" height="100" rx="8"></rect>
                <rect id="Ribbon-Vertical" fill="#FFD700" x="-8" y="-60" width="16" height="120"></rect>
                <rect id="Ribbon-Horizontal" fill="#FFD700" x="-70" y="-8" width="140" height="16"></rect>
                <circle id="Bow-Center" fill="#FFC300" cx="0" cy="0" r="10"></circle>
                <ellipse id="Bow-Left" fill="#FFC300" cx="-15" cy="-8" rx="10" ry="7" transform="rotate(-30)"></ellipse>
                <ellipse id="Bow-Right" fill="#FFC300" cx="15" cy="-8" rx="10" ry="7" transform="rotate(30)"></ellipse>
            </g>
            
            <!-- Small Gift Box -->
            <g id="Small-Gift" transform="translate(600, 380)">
                <rect id="Box" fill="url(#linearGradient-3)" x="-40" y="-30" width="80" height="60" rx="6"></rect>
                <rect id="Ribbon-Vertical" fill="#9B59B6" x="-6" y="-40" width="12" height="80"></rect>
                <rect id="Ribbon-Horizontal" fill="#9B59B6" x="-50" y="-6" width="100" height="12"></rect>
                <circle id="Bow-Center" fill="#8E44AD" cx="0" cy="0" r="8"></circle>
                <ellipse id="Bow-Left" fill="#8E44AD" cx="-12" cy="-6" rx="8" ry="5" transform="rotate(-30)"></ellipse>
                <ellipse id="Bow-Right" fill="#8E44AD" cx="12" cy="-6" rx="8" ry="5" transform="rotate(30)"></ellipse>
            </g>
            
            <!-- Stars/Sparkles -->
            <g id="Sparkles" fill="#FFD700">
                <path d="M150,150 L160,160 M140,160 L150,150 M150,140 L160,150 M140,150 L150,160" stroke="#FFD700" stroke-width="3"></path>
                <path d="M650,200 L660,210 M640,210 L650,200 M650,190 L660,200 M640,200 L650,210" stroke="#FFD700" stroke-width="3"></path>
                <path d="M350,450 L360,460 M340,460 L350,450 M350,440 L360,450 M340,450 L350,460" stroke="#FFD700" stroke-width="3"></path>
                <circle cx="100" cy="250" r="5"></circle>
                <circle cx="700" cy="150" r="5"></circle>
                <circle cx="500" cy="100" r="5"></circle>
                <circle cx="300" cy="500" r="5"></circle>
                <circle cx="600" cy="450" r="5"></circle>
            </g>
        </g>
    </g>
</svg>
