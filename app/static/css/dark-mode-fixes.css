/* Dark Mode Fixes for Tabler Elements */

/* Ensure dark mode works for specific elements that might not be covered by Tabler CDN */

/* Body and main background */
[data-bs-theme="dark"] body,
.theme-dark body {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
}

/* Navbar dark mode */
[data-bs-theme="dark"] .navbar,
.theme-dark .navbar {
  background-color: #0f172a !important;
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .navbar .nav-link,
.theme-dark .navbar .nav-link {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .navbar .nav-link:hover,
.theme-dark .navbar .nav-link:hover {
  color: #93c5fd !important;
}

[data-bs-theme="dark"] .navbar-brand,
.theme-dark .navbar-brand {
  color: #e2e8f0 !important;
}

/* Breadcrumbs dark mode */
[data-bs-theme="dark"] .breadcrumb,
.theme-dark .breadcrumb {
  background-color: #475569 !important;
}

[data-bs-theme="dark"] .breadcrumb-item a,
.theme-dark .breadcrumb-item a {
  color: #60a5fa !important;
}

[data-bs-theme="dark"] .breadcrumb-item.active,
.theme-dark .breadcrumb-item.active {
  color: #e2e8f0 !important;
}

/* Cards dark mode */
[data-bs-theme="dark"] .card,
.theme-dark .card {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .card-header,
.theme-dark .card-header {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

/* List groups dark mode */
[data-bs-theme="dark"] .list-group-item,
.theme-dark .list-group-item {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .list-group-item:hover,
.theme-dark .list-group-item:hover {
  background-color: #475569 !important;
}

/* Links dark mode */
[data-bs-theme="dark"] a,
.theme-dark a {
  color: #60a5fa !important;
}

[data-bs-theme="dark"] a:hover,
.theme-dark a:hover {
  color: #93c5fd !important;
}

/* Buttons dark mode */
[data-bs-theme="dark"] .btn,
.theme-dark .btn {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
  border-color: #64748b !important;
}

[data-bs-theme="dark"] .btn:hover,
.theme-dark .btn:hover {
  background-color: #64748b !important;
}

[data-bs-theme="dark"] .btn-primary,
.theme-dark .btn-primary {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
}

[data-bs-theme="dark"] .btn-primary:hover,
.theme-dark .btn-primary:hover {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

/* Forms dark mode */
[data-bs-theme="dark"] .form-control,
.theme-dark .form-control {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
  border-color: #64748b !important;
}

[data-bs-theme="dark"] .form-control:focus,
.theme-dark .form-control:focus {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

/* Dropdowns dark mode */
[data-bs-theme="dark"] .dropdown-menu,
.theme-dark .dropdown-menu {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .dropdown-item,
.theme-dark .dropdown-item {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .dropdown-item:hover,
.theme-dark .dropdown-item:hover {
  background-color: #475569 !important;
  color: #93c5fd !important;
}

/* Text colors */
[data-bs-theme="dark"] .text-muted,
.theme-dark .text-muted {
  color: #94a3b8 !important;
}

/* Page wrapper */
[data-bs-theme="dark"] .page,
.theme-dark .page {
  background-color: #1e293b !important;
}

/* Sticky navbar background fix */
[data-bs-theme="dark"] .navbar-sticky[data-bs-sticky],
.theme-dark .navbar-sticky[data-bs-sticky] {
  background-color: #0f172a !important;
}

/* Badges */
[data-bs-theme="dark"] .badge,
.theme-dark .badge {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
}

/* Alerts */
[data-bs-theme="dark"] .alert,
.theme-dark .alert {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

/* Tables */
[data-bs-theme="dark"] .table,
.theme-dark .table {
  color: #e2e8f0 !important;
  background-color: #334155 !important;
}

[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td,
[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > th,
.theme-dark .table-striped > tbody > tr:nth-of-type(odd) > td,
.theme-dark .table-striped > tbody > tr:nth-of-type(odd) > th {
  background-color: #475569 !important;
}

/* Footer */
[data-bs-theme="dark"] .footer,
.theme-dark .footer {
  background-color: #0f172a !important;
  color: #e2e8f0 !important;
}

/* Modals */
[data-bs-theme="dark"] .modal-content,
.theme-dark .modal-content {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .modal-header,
.theme-dark .modal-header {
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .modal-footer,
.theme-dark .modal-footer {
  border-color: #475569 !important;
}
