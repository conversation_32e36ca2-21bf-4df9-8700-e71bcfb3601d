/* Dark Mode Fixes for Tabler Elements */

/* Ensure dark mode works for specific elements that might not be covered by Tabler CDN */

/* Body and main background */
[data-bs-theme="dark"] body,
.theme-dark body {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
}

/* Navbar dark mode */
[data-bs-theme="dark"] .navbar,
.theme-dark .navbar {
  background-color: #0f172a !important;
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .navbar .nav-link,
.theme-dark .navbar .nav-link {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .navbar .nav-link:hover,
.theme-dark .navbar .nav-link:hover {
  color: #93c5fd !important;
}

[data-bs-theme="dark"] .navbar-brand,
.theme-dark .navbar-brand {
  color: #e2e8f0 !important;
}

/* Breadcrumbs dark mode */
[data-bs-theme="dark"] .breadcrumb,
.theme-dark .breadcrumb {
  background-color: #475569 !important;
}

[data-bs-theme="dark"] .breadcrumb-item a,
.theme-dark .breadcrumb-item a {
  color: #60a5fa !important;
}

[data-bs-theme="dark"] .breadcrumb-item.active,
.theme-dark .breadcrumb-item.active {
  color: #e2e8f0 !important;
}

/* Cards dark mode */
[data-bs-theme="dark"] .card,
.theme-dark .card {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .card-header,
.theme-dark .card-header {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

/* List groups dark mode */
[data-bs-theme="dark"] .list-group-item,
.theme-dark .list-group-item {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .list-group-item:hover,
.theme-dark .list-group-item:hover {
  background-color: #475569 !important;
}

/* Links dark mode */
[data-bs-theme="dark"] a,
.theme-dark a {
  color: #60a5fa !important;
}

[data-bs-theme="dark"] a:hover,
.theme-dark a:hover {
  color: #93c5fd !important;
}

/* Buttons dark mode */
[data-bs-theme="dark"] .btn,
.theme-dark .btn {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
  border-color: #64748b !important;
}

[data-bs-theme="dark"] .btn:hover,
.theme-dark .btn:hover {
  background-color: #64748b !important;
}

[data-bs-theme="dark"] .btn-primary,
.theme-dark .btn-primary {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
}

[data-bs-theme="dark"] .btn-primary:hover,
.theme-dark .btn-primary:hover {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

/* Forms dark mode */
[data-bs-theme="dark"] .form-control,
.theme-dark .form-control {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
  border-color: #64748b !important;
}

[data-bs-theme="dark"] .form-control:focus,
.theme-dark .form-control:focus {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

/* Dropdowns dark mode */
[data-bs-theme="dark"] .dropdown-menu,
.theme-dark .dropdown-menu {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .dropdown-item,
.theme-dark .dropdown-item {
  color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .dropdown-item:hover,
.theme-dark .dropdown-item:hover {
  background-color: #475569 !important;
  color: #93c5fd !important;
}

/* Text colors */
[data-bs-theme="dark"] .text-muted,
.theme-dark .text-muted {
  color: #94a3b8 !important;
}

/* Page wrapper */
[data-bs-theme="dark"] .page,
.theme-dark .page {
  background-color: #1e293b !important;
}

/* Sticky navbar background fix */
[data-bs-theme="dark"] .navbar-sticky[data-bs-sticky],
.theme-dark .navbar-sticky[data-bs-sticky] {
  background-color: #0f172a !important;
}

/* Badges */
[data-bs-theme="dark"] .badge,
.theme-dark .badge {
  background-color: #475569 !important;
  color: #e2e8f0 !important;
}

/* Alerts */
[data-bs-theme="dark"] .alert,
.theme-dark .alert {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

/* Tables */
[data-bs-theme="dark"] .table,
.theme-dark .table {
  color: #e2e8f0 !important;
  background-color: #334155 !important;
}

[data-bs-theme="dark"] .table-striped>tbody>tr:nth-of-type(odd)>td,
[data-bs-theme="dark"] .table-striped>tbody>tr:nth-of-type(odd)>th,
.theme-dark .table-striped>tbody>tr:nth-of-type(odd)>td,
.theme-dark .table-striped>tbody>tr:nth-of-type(odd)>th {
  background-color: #475569 !important;
}

/* Footer */
[data-bs-theme="dark"] .footer,
.theme-dark .footer {
  background-color: #0f172a !important;
  color: #e2e8f0 !important;
}

/* Modals */
[data-bs-theme="dark"] .modal-content,
.theme-dark .modal-content {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .modal-header,
.theme-dark .modal-header {
  border-color: #475569 !important;
}

[data-bs-theme="dark"] .modal-footer,
.theme-dark .modal-footer {
  border-color: #475569 !important;
}

/* Home page specific sections */
[data-bs-theme="dark"] .bg-white,
.theme-dark .bg-white {
  background-color: #334155 !important;
}

[data-bs-theme="dark"] .bg-light,
.theme-dark .bg-light {
  background-color: #475569 !important;
}

/* Container backgrounds */
[data-bs-theme="dark"] .container-xl,
[data-bs-theme="dark"] .container,
.theme-dark .container-xl,
.theme-dark .container {
  background-color: transparent !important;
}

/* Section backgrounds */
[data-bs-theme="dark"] section.bg-white,
.theme-dark section.bg-white {
  background-color: #334155 !important;
}

[data-bs-theme="dark"] section.bg-light,
.theme-dark section.bg-light {
  background-color: #475569 !important;
}

/* Card shadows in dark mode */
[data-bs-theme="dark"] .shadow-sm,
.theme-dark .shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.5) !important;
}

/* Form check labels */
[data-bs-theme="dark"] .form-check-label,
.theme-dark .form-check-label {
  color: #e2e8f0 !important;
}

/* Form text */
[data-bs-theme="dark"] .form-text,
.theme-dark .form-text {
  color: #94a3b8 !important;
}

/* Alert warning in dark mode */
[data-bs-theme="dark"] .alert-warning,
.theme-dark .alert-warning {
  background-color: #451a03 !important;
  border-color: #92400e !important;
  color: #fbbf24 !important;
}

/* Text colors for headings */
[data-bs-theme="dark"] h1,
[data-bs-theme="dark"] h2,
[data-bs-theme="dark"] h3,
[data-bs-theme="dark"] h4,
[data-bs-theme="dark"] h5,
[data-bs-theme="dark"] h6,
.theme-dark h1,
.theme-dark h2,
.theme-dark h3,
.theme-dark h4,
.theme-dark h5,
.theme-dark h6 {
  color: #e2e8f0 !important;
}

/* Lead text */
[data-bs-theme="dark"] .lead,
.theme-dark .lead {
  color: #cbd5e1 !important;
}

/* Display text */
[data-bs-theme="dark"] .display-4,
.theme-dark .display-4 {
  color: #e2e8f0 !important;
}

/* Card title links */
[data-bs-theme="dark"] .card-title a,
.theme-dark .card-title a {
  color: #60a5fa !important;
}

[data-bs-theme="dark"] .card-title a:hover,
.theme-dark .card-title a:hover {
  color: #93c5fd !important;
}

/* Stretched links */
[data-bs-theme="dark"] .stretched-link,
.theme-dark .stretched-link {
  color: #60a5fa !important;
}

/* Text primary in dark mode */
[data-bs-theme="dark"] .text-primary,
.theme-dark .text-primary {
  color: #60a5fa !important;
}

/* Small text */
[data-bs-theme="dark"] .small,
[data-bs-theme="dark"] small,
.theme-dark .small,
.theme-dark small {
  color: #94a3b8 !important;
}

/* Ribbon Component - Tabler Style */
.ribbon {
  position: absolute;
  z-index: 2;
  padding: 0.375rem 0.75rem;
  font-size: 0.6875rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-radius: 0.25rem;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.ribbon-top {
  top: 0.5rem;
}

.ribbon-end {
  right: -0.125rem;
}

.ribbon.bg-danger {
  background-color: #dc3545 !important;
}

.ribbon.bg-success {
  background-color: #198754 !important;
}

/* Dark mode ribbon adjustments */
[data-bs-theme="dark"] .ribbon.bg-danger,
.theme-dark .ribbon.bg-danger {
  background-color: #dc3545 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.4);
}

[data-bs-theme="dark"] .ribbon.bg-success,
.theme-dark .ribbon.bg-success {
  background-color: #198754 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.4);
}

/* Ensure card overflow is visible for ribbon */
.card.position-relative {
  overflow: visible;
}