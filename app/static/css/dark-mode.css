/* Dark Mode CSS for Tabler */

/* Dark mode variables */
.theme-dark {
  --tblr-body-bg: #1e293b;
  --tblr-body-color: #cbd5e1;
  --tblr-card-bg: #334155;
  --tblr-border-color: #475569;
  --tblr-navbar-bg: #0f172a;
  --tblr-navbar-color: #e2e8f0;
  --tblr-breadcrumb-bg: #475569;
  --tblr-breadcrumb-color: #e2e8f0;
  --tblr-link-color: #60a5fa;
  --tblr-link-hover-color: #93c5fd;
  --tblr-btn-bg: #475569;
  --tblr-btn-color: #e2e8f0;
  --tblr-btn-border-color: #64748b;
  --tblr-input-bg: #475569;
  --tblr-input-color: #e2e8f0;
  --tblr-input-border-color: #64748b;
  --tblr-table-bg: #334155;
  --tblr-table-striped-bg: #475569;
  --tblr-dropdown-bg: #334155;
  --tblr-dropdown-border-color: #475569;
  --tblr-modal-bg: #334155;
  --tblr-footer-bg: #0f172a;
}

/* Body and main background */
.theme-dark body {
  background-color: var(--tblr-body-bg) !important;
  color: var(--tblr-body-color) !important;
}

/* Navbar */
.theme-dark .navbar {
  background-color: var(--tblr-navbar-bg) !important;
  border-color: var(--tblr-border-color) !important;
}

.theme-dark .navbar .nav-link {
  color: var(--tblr-navbar-color) !important;
}

.theme-dark .navbar .nav-link:hover {
  color: var(--tblr-link-hover-color) !important;
}

.theme-dark .navbar-brand {
  color: var(--tblr-navbar-color) !important;
}

/* Cards */
.theme-dark .card {
  background-color: var(--tblr-card-bg) !important;
  border-color: var(--tblr-border-color) !important;
  color: var(--tblr-body-color) !important;
}

.theme-dark .card-header {
  background-color: var(--tblr-card-bg) !important;
  border-color: var(--tblr-border-color) !important;
}

.theme-dark .card-body {
  background-color: var(--tblr-card-bg) !important;
}

/* Breadcrumbs */
.theme-dark .breadcrumb {
  background-color: var(--tblr-breadcrumb-bg) !important;
}

.theme-dark .breadcrumb-item a {
  color: var(--tblr-link-color) !important;
}

.theme-dark .breadcrumb-item.active {
  color: var(--tblr-breadcrumb-color) !important;
}

/* Links */
.theme-dark a {
  color: var(--tblr-link-color) !important;
}

.theme-dark a:hover {
  color: var(--tblr-link-hover-color) !important;
}

/* Buttons */
.theme-dark .btn {
  background-color: var(--tblr-btn-bg) !important;
  color: var(--tblr-btn-color) !important;
  border-color: var(--tblr-btn-border-color) !important;
}

.theme-dark .btn:hover {
  background-color: var(--tblr-border-color) !important;
}

.theme-dark .btn-primary {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
}

.theme-dark .btn-primary:hover {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

/* Forms */
.theme-dark .form-control {
  background-color: var(--tblr-input-bg) !important;
  color: var(--tblr-input-color) !important;
  border-color: var(--tblr-input-border-color) !important;
}

.theme-dark .form-control:focus {
  background-color: var(--tblr-input-bg) !important;
  color: var(--tblr-input-color) !important;
  border-color: var(--tblr-link-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

.theme-dark .form-select {
  background-color: var(--tblr-input-bg) !important;
  color: var(--tblr-input-color) !important;
  border-color: var(--tblr-input-border-color) !important;
}

/* Tables */
.theme-dark .table {
  color: var(--tblr-body-color) !important;
  background-color: var(--tblr-table-bg) !important;
}

.theme-dark .table-striped > tbody > tr:nth-of-type(odd) > td,
.theme-dark .table-striped > tbody > tr:nth-of-type(odd) > th {
  background-color: var(--tblr-table-striped-bg) !important;
}

/* Dropdowns */
.theme-dark .dropdown-menu {
  background-color: var(--tblr-dropdown-bg) !important;
  border-color: var(--tblr-dropdown-border-color) !important;
}

.theme-dark .dropdown-item {
  color: var(--tblr-body-color) !important;
}

.theme-dark .dropdown-item:hover {
  background-color: var(--tblr-border-color) !important;
  color: var(--tblr-link-hover-color) !important;
}

/* Modals */
.theme-dark .modal-content {
  background-color: var(--tblr-modal-bg) !important;
  border-color: var(--tblr-border-color) !important;
}

.theme-dark .modal-header {
  border-color: var(--tblr-border-color) !important;
}

.theme-dark .modal-footer {
  border-color: var(--tblr-border-color) !important;
}

/* Footer */
.theme-dark .footer {
  background-color: var(--tblr-footer-bg) !important;
  color: var(--tblr-body-color) !important;
}

/* Text colors */
.theme-dark .text-muted {
  color: #94a3b8 !important;
}

.theme-dark .text-primary {
  color: var(--tblr-link-color) !important;
}

/* Badges */
.theme-dark .badge {
  background-color: var(--tblr-btn-bg) !important;
  color: var(--tblr-btn-color) !important;
}

/* Alerts */
.theme-dark .alert {
  background-color: var(--tblr-card-bg) !important;
  border-color: var(--tblr-border-color) !important;
  color: var(--tblr-body-color) !important;
}

/* List groups */
.theme-dark .list-group-item {
  background-color: var(--tblr-card-bg) !important;
  border-color: var(--tblr-border-color) !important;
  color: var(--tblr-body-color) !important;
}

/* Page wrapper */
.theme-dark .page {
  background-color: var(--tblr-body-bg) !important;
}

/* Sticky navbar background fix */
.theme-dark .navbar-sticky[data-bs-sticky] {
  background-color: var(--tblr-navbar-bg) !important;
}

/* Custom breadcrumb card styling */
.theme-dark .card[style*="background: #f8fafc"] {
  background-color: var(--tblr-breadcrumb-bg) !important;
}

/* Override inline styles */
.theme-dark [style*="background: #f8fafc"] {
  background-color: var(--tblr-breadcrumb-bg) !important;
}
