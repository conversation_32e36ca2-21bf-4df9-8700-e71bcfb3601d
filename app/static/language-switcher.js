// Language switcher functionality
document.addEventListener('DOMContentLoaded', function () {
    // Language values
    const LANG_EN = 'en';
    const LANG_PL = 'pl';

    // Get the language toggle button
    const langToggle = document.getElementById('language-toggle');
    const langIcon = document.getElementById('language-icon');
    const langText = document.getElementById('language-text');

    // Function to set language
    function setLanguage(lang) {
        console.log('Setting language to:', lang);
        // Save the language preference
        localStorage.setItem('language-preference', lang);

        // Update toggle button
        if (lang === LANG_PL) {
            langIcon.className = 'bi bi-translate';
            langText.textContent = 'Polski';
        } else {
            langIcon.className = 'bi bi-translate';
            langText.textContent = 'English';
        }

        // Redirect to change language on server
        const url = '/language/' + lang;
        console.log('Redirecting to:', url);

        // Use direct navigation with a timestamp to prevent caching
        window.location.href = url + '?t=' + new Date().getTime();
    }

    // Initialize language based on saved preference
    function initializeLanguage() {
        const savedLang = localStorage.getItem('language-preference');
        const currentLang = document.documentElement.lang;

        if (savedLang && savedLang !== currentLang) {
            setLanguage(savedLang);
        } else if (currentLang === LANG_PL) {
            langIcon.className = 'bi bi-translate';
            langText.textContent = 'Polski';
        } else {
            langIcon.className = 'bi bi-translate';
            langText.textContent = 'English';
        }
    }

    // Toggle between languages (English <-> Polish)
    function toggleLanguage() {
        const currentLang = document.documentElement.lang || LANG_EN;
        console.log('Current language:', currentLang);

        if (currentLang === LANG_EN) {
            console.log('Switching to Polish');
            setLanguage(LANG_PL);
        } else {
            console.log('Switching to English');
            setLanguage(LANG_EN);
        }
    }

    // Add event listener to language toggle button
    if (langToggle) {
        langToggle.addEventListener('click', toggleLanguage);
    }

    // Initialize language on page load
    initializeLanguage();
});
