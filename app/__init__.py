from flask import Flask, request, session, redirect, url_for, g, flash
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user
from flask_wtf import CSRFProtect
from flask_bootstrap import <PERSON>tra<PERSON>5
from config import config
from app.translations import get_translation

# Create extensions instances
db = SQLAlchemy()
migrate = Migrate()
bootstrap = Bootstrap5()
csrf = CSRFProtect()
login_manager = LoginManager()

def get_current_language():
    """Get the current language from URL, session, or browser preference."""
    # First, try to get from URL parameter
    lang = request.args.get('lang')
    if lang in ['en', 'pl']:
        return lang

    # Then, try to get the language from the session
    if 'language' in session:
        lang = session['language']
        if lang in ['en', 'pl']:
            return lang

    # Otherwise, try to detect it from the request
    return request.accept_languages.best_match(['en', 'pl'], default='en')

def create_app(config_name='default'):
    app = Flask(__name__,
                static_folder='static',
                template_folder='templates')
    app.config.from_object(config[config_name])

    # Configure for proxy and HTTPS
    app.config['PREFERRED_URL_SCHEME'] = 'https'

    # Handle proxy headers
    from werkzeug.middleware.proxy_fix import ProxyFix
    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)

    # Set a secret key if not already set
    if not app.config.get('SECRET_KEY'):
        app.config['SECRET_KEY'] = 'dev-key-for-testing'

    # Ensure session works properly
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['PERMANENT_SESSION_LIFETIME'] = 3600  # 1 hour

    # Initialize extensions
    print("SQLALCHEMY_DATABASE_URI: {}".format(app.config.get('SQLALCHEMY_DATABASE_URI')))
    # Set database URI directly if not set
    if not app.config.get('SQLALCHEMY_DATABASE_URI'):
        app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:UF!<EMAIL>:13306/gifts_db?charset=utf8mb4&use_unicode=1&binary_prefix=true'
        print("Set SQLALCHEMY_DATABASE_URI to: {}".format(app.config.get('SQLALCHEMY_DATABASE_URI')))

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    bootstrap.init_app(app)
    csrf.init_app(app)

    # Initialize app with config-specific settings
    config[config_name].init_app(app)

    # Make the db instance available to the app
    app.db = db

    # Configure Flask-Login
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        from app.models import User
        return User.query.get(int(user_id))

    # Create a translation function that uses the current language
    def translate(text, **kwargs):
        lang = get_current_language()
        translated = get_translation(text, lang)
        # Handle string formatting with named parameters if kwargs are provided
        if kwargs:
            try:
                return translated % kwargs
            except (KeyError, TypeError) as e:
                # If formatting fails, return the original translated text
                print("Translation formatting error: {}".format(e))
                return translated
        return translated

    # Create a localized date formatting function
    def format_date(date_obj, format_type='short'):
        """Format date according to current language."""
        lang = get_current_language()

        if format_type == 'short':
            if lang == 'pl':
                # Polish format: "12 sty 2025"
                months_pl = {
                    1: 'sty', 2: 'lut', 3: 'mar', 4: 'kwi', 5: 'maj', 6: 'cze',
                    7: 'lip', 8: 'sie', 9: 'wrz', 10: 'paź', 11: 'lis', 12: 'gru'
                }
                return f"{date_obj.day} {months_pl[date_obj.month]} {date_obj.year}"
            else:
                # English format: "Jan 12, 2025"
                return date_obj.strftime('%b %d, %Y')
        elif format_type == 'long':
            if lang == 'pl':
                # Polish long format: "12 stycznia 2025"
                months_pl_long = {
                    1: 'stycznia', 2: 'lutego', 3: 'marca', 4: 'kwietnia', 5: 'maja', 6: 'czerwca',
                    7: 'lipca', 8: 'sierpnia', 9: 'września', 10: 'października', 11: 'listopada', 12: 'grudnia'
                }
                return f"{date_obj.day} {months_pl_long[date_obj.month]} {date_obj.year}"
            else:
                # English long format: "January 12, 2025"
                return date_obj.strftime('%B %d, %Y')
        else:
            # Default to ISO format
            return date_obj.strftime('%Y-%m-%d')

    # Make translation function and date formatter available in templates
    app.jinja_env.globals['_'] = translate
    app.jinja_env.globals['format_date'] = format_date

    # Set language for each request
    @app.before_request
    def set_language_for_request():
        g.lang_code = get_current_language()

    # Set theme for each request
    @app.before_request
    def set_theme_for_request():
        # Get theme from session or default to 'light'
        g.theme = session.get('preferred_theme', 'light')

        # If user is logged in, use their preferred theme
        if current_user.is_authenticated:
            g.theme = current_user.preferred_theme

    # Update login message based on current language in each request
    @app.before_request
    def update_login_message():
        login_manager.login_message = get_translation('Please log in to access this page.', g.lang_code)

    # Register blueprints
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    # Register OAuth blueprints
    from app.auth.oauth import google_bp
    app.register_blueprint(google_bp, url_prefix='/login')

    # Add route to change language
    @app.route('/language/<lang>')
    def set_language(lang):
        if lang not in ['en', 'pl']:
            lang = 'en'

        # Store in session as backup
        session['language'] = lang
        session.permanent = True

        # Get the next URL from the query parameter or default to index
        next_path = request.args.get('next', '')

        # If next is provided and is a valid path, use it
        if next_path and next_path.startswith('/'):
            # Add language parameter to the URL
            if '?' in next_path:
                # URL already has parameters
                if 'lang=' in next_path:
                    # Replace existing lang parameter
                    next_path = next_path.replace("lang={}".format(session.get('language', 'en')), "lang={}".format(lang))
                else:
                    # Add lang parameter
                    next_path += "&lang={}".format(lang)
            else:
                # No parameters yet
                next_path += "?lang={}".format(lang)

            return redirect(next_path)
        else:
            # Default to index if no valid next path
            return redirect(url_for('main.index', lang=lang))

    return app
