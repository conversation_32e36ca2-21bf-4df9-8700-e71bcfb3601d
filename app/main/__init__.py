from flask import Blueprint, g, request, session

bp = Blueprint('main', __name__)

@bp.before_app_request
def before_request():
    # Make the current language available to templates
    # First check URL parameter
    lang = request.args.get('lang')
    if lang not in ['en', 'pl']:
        # Then check session
        lang = session.get('language')
        if lang not in ['en', 'pl']:
            # Finally fall back to browser preference
            lang = request.accept_languages.best_match(['en', 'pl'], default='en')

    # Store in global context for templates
    g.lang_code = lang

    # Also store in session for persistence
    if lang != session.get('language'):
        session['language'] = lang
        session.permanent = True

from . import views
