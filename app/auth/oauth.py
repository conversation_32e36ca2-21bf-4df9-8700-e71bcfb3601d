from flask import flash, redirect, url_for, session, current_app
from flask_dance.contrib.google import make_google_blueprint, google
from flask_dance.consumer import oauth_authorized
from flask_dance.consumer.storage.sqla import SQLAlchemyStorage
from flask_login import login_user, current_user
from sqlalchemy.orm.exc import NoResultFound
from app import db
from app.models import User, OAuth
from app.translations import get_translation
from datetime import datetime

# Helper function for translation
def _(text):
    """Translate text using the current language"""
    from flask import g
    if hasattr(g, 'lang_code'):
        return get_translation(text, g.lang_code)
    return text

# Create the blueprint for Google OAuth
google_bp = make_google_blueprint(
    scope=["https://www.googleapis.com/auth/userinfo.profile",
           "https://www.googleapis.com/auth/userinfo.email",
           "openid"],
    storage=SQLAlchemyStorage(OAuth, db.session),
    redirect_to="main.index"  # Redirect to home page after login
)

@oauth_authorized.connect_via(google_bp)
def google_logged_in(blueprint, token):
    if not token:
        flash(_("Failed to log in with Google."), "danger")
        return False

    # Get Google user info
    resp = google.get("/oauth2/v2/userinfo")
    if not resp.ok:
        flash(_("Failed to fetch user info from Google."), "danger")
        return False

    google_info = resp.json()
    google_user_id = google_info["id"]
    google_email = google_info.get("email")
    google_name = google_info.get("name", "").split()[0] if "name" in google_info and google_info["name"] else ""

    # Find or create user
    try:
        # Try to find existing user by Google ID or email
        user = User.query.filter_by(email=google_email).first()

        if user:
            # Update existing user with Google info if needed
            user.last_seen = datetime.utcnow()

            # Check if OAuth record exists, create if not
            oauth = OAuth.query.filter_by(
                provider="google",
                user_id=user.id
            ).first()

            if not oauth:
                # Create OAuth record for existing user
                oauth = OAuth(
                    provider="google",
                    user_id=user.id,
                    token=token
                )
                db.session.add(oauth)
        else:
            # Create a new user
            username = f"{google_name}{google_user_id[-5:]}" if google_name else f"user{google_user_id[-8:]}"
            # Make sure username is unique
            while User.query.filter_by(username=username).first():
                username = f"{google_name}{google_user_id[-8:]}"

            user = User(
                email=google_email,
                username=username,
                preferred_theme='os',
                preferred_language='en'
            )
            # Set a random password (user won't use it but we need it for the model)
            import secrets
            user.set_password(secrets.token_urlsafe(16))
            db.session.add(user)

            # Flush to get the user ID before creating OAuth record
            db.session.flush()

            # Create OAuth record for new user
            oauth = OAuth(
                provider="google",
                user_id=user.id,
                token=token
            )
            db.session.add(oauth)

        db.session.commit()

        # Log in the user
        login_user(user)

        # Set user preferences in session
        if user.preferred_language:
            session['language'] = user.preferred_language
        if user.preferred_theme:
            session['preferred_theme'] = user.preferred_theme

        flash(_("Successfully signed in with Google."), "success")

    except Exception as e:
        db.session.rollback()
        flash(_("An error occurred during Google sign-in."), "danger")
        current_app.logger.error(f"Google OAuth error: {str(e)}")
        return False

    # Skip the default OAuth handling since we did it ourselves
    return False
