#!/usr/bin/env python
import os
from app import create_app, db
from app.models import User, GiftList, Gift
import uuid

# Get configuration from environment variable or use default
config_name = os.getenv('FLASK_CONFIG', 'default')
app = create_app(config_name)

with app.app_context():
    # Get the test user
    test_user = User.query.filter_by(email='<EMAIL>').first()
    if not test_user:
        print("Test user not found")
        exit(1)
    
    print(f"Testing gift list functionality for user: {test_user.username}")
    
    # Create a private gift list
    private_list = GiftList(
        name="Private Birthday Wishlist",
        user_id=test_user.id,
        is_private=True,
        share_code=str(uuid.uuid4())[:8]
    )
    db.session.add(private_list)
    
    # Create a public gift list
    public_list = GiftList(
        name="Public Wedding Registry",
        user_id=test_user.id,
        is_private=False
    )
    db.session.add(public_list)
    db.session.commit()
    
    print(f"Created private list: {private_list.name} (ID: {private_list.id})")
    print(f"Share code: {private_list.share_code}")
    print(f"Created public list: {public_list.name} (ID: {public_list.id})")
    
    # Add gifts to the private list
    gift1 = Gift(
        name="PlayStation 5",
        link="https://www.amazon.com/PlayStation-5-Console/dp/B08FC5L3RG",
        gift_list_id=private_list.id
    )
    gift2 = Gift(
        name="Nintendo Switch",
        link="https://www.amazon.com/Nintendo-Switch-Neon-Blue-Red-Joy%E2%80%91/dp/B07VGRJDFY",
        gift_list_id=private_list.id
    )
    db.session.add(gift1)
    db.session.add(gift2)
    
    # Add gifts to the public list
    gift3 = Gift(
        name="KitchenAid Stand Mixer",
        link="https://www.amazon.com/KitchenAid-KSM150PSER-Artisan-Tilt-Head-Pouring/dp/B00005UP2P",
        gift_list_id=public_list.id
    )
    gift4 = Gift(
        name="Dyson Vacuum Cleaner",
        link="https://www.amazon.com/Dyson-Ball-Animal-Upright-Vacuum/dp/B01NAGRRRU",
        gift_list_id=public_list.id
    )
    db.session.add(gift3)
    db.session.add(gift4)
    db.session.commit()
    
    print(f"Added gifts to private list:")
    for gift in private_list.gifts:
        print(f"- {gift.name}")
    
    print(f"Added gifts to public list:")
    for gift in public_list.gifts:
        print(f"- {gift.name}")
    
    # List all gift lists for the user
    print(f"\nAll gift lists for {test_user.username}:")
    for gift_list in test_user.gift_lists:
        print(f"- {gift_list.name} ({'Private' if gift_list.is_private else 'Public'})")
        print(f"  Gifts: {gift_list.gifts.count()}")
