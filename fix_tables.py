from app import create_app, db
from sqlalchemy import text

app = create_app()

def reset_database():
    with app.app_context():
        # Disable foreign key checks
        db.session.execute(text('SET FOREIGN_KEY_CHECKS=0'))
        
        # Drop and recreate tables
        db.session.execute(text('DROP TABLE IF EXISTS gift'))
        db.session.execute(text('DROP TABLE IF EXISTS gift_list'))
        
        # Re-enable foreign key checks
        db.session.execute(text('SET FOREIGN_KEY_CHECKS=1'))
        
        # Create tables from models
        db.create_all()
        
        db.session.commit()
        
        print("Tables recreated successfully!")

if __name__ == "__main__":
    reset_database()
