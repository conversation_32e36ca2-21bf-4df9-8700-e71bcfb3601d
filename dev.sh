#!/bin/bash
# Development helper script for FiestaMagic

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print section header
print_header() {
    echo -e "\n${GRE<PERSON>}==== $1 ====${NC}\n"
}

# Function to print error
print_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

# Check if virtual environment is activated
check_venv() {
    if [ -z "$VIRTUAL_ENV" ]; then
        print_warning "Virtual environment is not activated. Activating now..."
        source .venv/bin/activate || {
            print_error "Failed to activate virtual environment. Please run: source .venv/bin/activate"
            exit 1
        }
    fi
}

# Setup virtual environment
setup_venv() {
    print_header "Setting up virtual environment"
    
    if [ -d ".venv" ]; then
        print_warning "Virtual environment already exists. Skipping creation."
    else
        python -m venv .venv
        echo "Virtual environment created in .venv/"
    fi
    
    check_venv
    
    pip install --upgrade pip
    pip install -r requirements.txt
    
    echo -e "\n${GREEN}Virtual environment setup complete!${NC}"
}

# Run the application
run_app() {
    print_header "Running the application"
    check_venv
    python run.py
}

# Run the application in debug mode
run_debug() {
    print_header "Running the application in debug mode"
    check_venv
    FLASK_DEBUG=1 python run.py
}

# Create a database migration
db_migrate() {
    print_header "Creating database migration"
    check_venv
    
    if [ -z "$1" ]; then
        print_error "Migration message is required. Usage: ./dev.sh db:migrate \"Your migration message\""
        exit 1
    fi
    
    flask db migrate -m "$1"
    
    echo -e "\n${GREEN}Migration created. Review the migration file before applying it.${NC}"
}

# Apply database migrations
db_upgrade() {
    print_header "Applying database migrations"
    check_venv
    flask db upgrade
    echo -e "\n${GREEN}Migrations applied successfully!${NC}"
}

# Downgrade database
db_downgrade() {
    print_header "Downgrading database"
    check_venv
    
    if [ -z "$1" ]; then
        flask db downgrade
    else
        flask db downgrade "$1"
    fi
    
    echo -e "\n${GREEN}Database downgraded successfully!${NC}"
}

# Show migration history
db_history() {
    print_header "Migration history"
    check_venv
    flask db history
}

# Update translations
update_translations() {
    print_header "Updating translations"
    check_venv
    python update_translations.py
    echo -e "\n${GREEN}Translations updated. Check app/translations.py for any untranslated strings.${NC}"
}

# Reset database
reset_db() {
    print_header "Resetting database"
    check_venv
    
    read -p "This will delete all data in the database. Are you sure? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        python fix_tables.py
        echo -e "\n${GREEN}Database reset successfully!${NC}"
    else
        echo -e "\n${YELLOW}Database reset cancelled.${NC}"
    fi
}

# Run tests
run_tests() {
    print_header "Running tests"
    check_venv
    pytest
    echo -e "\n${GREEN}Tests completed!${NC}"
}

# Show help
show_help() {
    echo -e "${GREEN}FiestaMagic Development Helper${NC}"
    echo -e "Usage: ./dev.sh [command]"
    echo
    echo -e "Available commands:"
    echo -e "  ${YELLOW}setup${NC}              Setup virtual environment and install dependencies"
    echo -e "  ${YELLOW}run${NC}                Run the application"
    echo -e "  ${YELLOW}debug${NC}              Run the application in debug mode"
    echo -e "  ${YELLOW}db:migrate${NC} [msg]   Create a database migration with message"
    echo -e "  ${YELLOW}db:upgrade${NC}         Apply database migrations"
    echo -e "  ${YELLOW}db:downgrade${NC} [rev] Downgrade database to previous revision or specific revision"
    echo -e "  ${YELLOW}db:history${NC}         Show migration history"
    echo -e "  ${YELLOW}db:reset${NC}           Reset database (WARNING: deletes all data)"
    echo -e "  ${YELLOW}translations${NC}       Update translations"
    echo -e "  ${YELLOW}test${NC}               Run tests"
    echo -e "  ${YELLOW}help${NC}               Show this help message"
}

# Main script logic
case "$1" in
    setup)
        setup_venv
        ;;
    run)
        run_app
        ;;
    debug)
        run_debug
        ;;
    db:migrate)
        db_migrate "$2"
        ;;
    db:upgrade)
        db_upgrade
        ;;
    db:downgrade)
        db_downgrade "$2"
        ;;
    db:history)
        db_history
        ;;
    db:reset)
        reset_db
        ;;
    translations)
        update_translations
        ;;
    test)
        run_tests
        ;;
    help|*)
        show_help
        ;;
esac
