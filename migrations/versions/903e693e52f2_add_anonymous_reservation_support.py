"""Add anonymous reservation support

Revision ID: 903e693e52f2
Revises: eb71ca80b79c
Create Date: 2025-04-23 18:24:28.156897

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '903e693e52f2'
down_revision = 'eb71ca80b79c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('gifts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('anonymous_token', sa.String(length=64), nullable=True))
        batch_op.add_column(sa.Column('reserver_name', sa.String(length=100), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('gifts', schema=None) as batch_op:
        batch_op.drop_column('reserver_name')
        batch_op.drop_column('anonymous_token')

    # ### end Alembic commands ###
