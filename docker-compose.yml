version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    # Load environment variables from .env file
    env_file:
      - .env
    # You can also set environment variables directly here
    # These will override any variables with the same name from the .env file
    environment:
      # Example of setting an environment variable directly
      - FLASK_ENV=development
      # OAuth configuration to handle scope changes
      - OAUTHLIB_RELAX_TOKEN_SCOPE=true
      - OAUTHLIB_INSECURE_TRANSPORT=false
      # Uncomment and modify these as needed
      # - FLASK_CONFIG=development
      # - SECRET_KEY=your-secret-key-here
      # - DATABASE_URL=mysql+pymysql://gifts_user:gifts_password@db:3306/gifts_db
    volumes:
      - .:/app
    depends_on:
      db:
        condition: service_healthy
    restart: always

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD:-rootpassword}
      - MYSQL_DATABASE=gifts_db
      - MYSQL_USER=gifts_user
      - MYSQL_PASSWORD=gifts_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_PASSWORD:-rootpassword}"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  mysql_data:
