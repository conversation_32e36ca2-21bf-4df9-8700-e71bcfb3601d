stages:
  - build
  - test
  - deploy

variables:
  DOCKER_TLS_CERTDIR: "/certs"
  CONTAINER_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG

# Use Docker-in-Docker service for building images
services:
  - docker:dind

build:
  stage: build
  image: docker:latest
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $CONTAINER_IMAGE .
    - docker push $CONTAINER_IMAGE
    # Create a tag with the commit SHA for immutable references
    - docker tag $CONTAINER_IMAGE $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    # Tag as 'latest' if on the main branch
    - if [ "$CI_COMMIT_REF_NAME" = "main" ]; then
        docker tag $CONTAINER_IMAGE $CI_REGISTRY_IMAGE:latest;
        docker push $CI_REGISTRY_IMAGE:latest;
      fi
  only:
    - main
    - tags
    - merge_requests

# test:
#   stage: test
#   image: python:3.9-slim
#   before_script:
#     - pip install -r requirements.txt
#     - pip install pytest pytest-cov  # Explicitly install pytest
#   script:
#     - python -m pytest  # Use python -m to ensure we're using the installed version
#   only:
#     - main
#     - tags
#     - merge_requests

deploy:
  stage: deploy
  image: alpine:latest
  script:
    - echo "Deploying application..."
    - echo "Using image $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG"
    # Add your deployment commands here
  environment:
    name: production
  only:
    - main
