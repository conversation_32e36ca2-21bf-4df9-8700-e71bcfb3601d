from app import create_app, db
from sqlalchemy import text

app = create_app()

with app.app_context():
    # Execute raw SQL to set the character set for the database
    with db.engine.connect() as conn:
        conn.execute(text("ALTER DATABASE gifts_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"))
        
        # Update existing tables
        conn.execute(text("ALTER TABLE gift_lists CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"))
        conn.execute(text("ALTER TABLE gifts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"))
        conn.execute(text("ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"))
        
        # Commit the transaction
        conn.commit()
    
    print("Database character set updated successfully!")
