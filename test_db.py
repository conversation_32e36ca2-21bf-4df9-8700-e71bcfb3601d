from app import create_app, db
from app.models import GiftList

app = create_app()

with app.app_context():
    try:
        # Try to connect to the database
        connection = db.engine.connect()
        print("Successfully connected to the database")
        
        # Try to query the database
        lists_count = GiftList.query.count()
        print(f"Number of gift lists in the database: {lists_count}")
        
        connection.close()
    except Exception as e:
        print(f"Error connecting to the database: {e}")
