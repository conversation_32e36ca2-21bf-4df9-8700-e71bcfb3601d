#!/usr/bin/env python
import os
from app import create_app, db
from app.models import User

# Get configuration from environment variable or use default
config_name = os.getenv('FLASK_CONFIG', 'default')
app = create_app(config_name)

with app.app_context():
    # Check if test user already exists
    test_user = User.query.filter_by(email='<EMAIL>').first()
    if test_user:
        print(f"Test user already exists: {test_user.username} ({test_user.email})")
    else:
        # Create test user
        test_user = User(
            username='testuser',
            email='<EMAIL>',
            preferred_theme='light',
            preferred_language='en'
        )
        test_user.set_password('password123')
        db.session.add(test_user)
        db.session.commit()
        print(f"Created test user: {test_user.username} ({test_user.email})")
        print(f"Password: password123")

    # List all users
    print("\nAll users:")
    users = User.query.all()
    for user in users:
        print(f"- {user.username} ({user.email})")
