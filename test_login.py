#!/usr/bin/env python
import os
from app import create_app, db
from app.models import User

# Get configuration from environment variable or use default
config_name = os.getenv('FLASK_CONFIG', 'default')
app = create_app(config_name)

with app.app_context():
    # Try to authenticate the test user
    test_user = User.query.filter_by(email='<EMAIL>').first()
    if test_user:
        if test_user.check_password('password123'):
            print(f"Authentication successful for user: {test_user.username}")
            print(f"User details:")
            print(f"- Email: {test_user.email}")
            print(f"- Preferred theme: {test_user.preferred_theme}")
            print(f"- Preferred language: {test_user.preferred_language}")
            print(f"- Created at: {test_user.created_at}")
            print(f"- Last seen: {test_user.last_seen}")
        else:
            print(f"Authentication failed for user: {test_user.username}")
    else:
        print("Test user not found")
